import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'dart:convert';

class SmsService {
  static final SmsService _instance = SmsService._internal();
  factory SmsService() => _instance;
  SmsService._internal();

  static const MethodChannel _channel = MethodChannel('sms_receiver');
  static Function(String, String)? onSmsReceived;

  Future<void> handleIncomingSms(Map<String, dynamic> smsData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedNumber = prefs.getString('phone_number');

      if (savedNumber != null) {
        final sender = smsData['sender'] as String? ?? '';
        final message = smsData['message'] as String? ?? '';

        // Check if the SMS is from the saved number
        String cleanSavedNumber = savedNumber.replaceAll(RegExp(r'[^\d]'), '');
        String cleanSenderNumber = sender.replaceAll(RegExp(r'[^\d]'), '');

        // Remove country code if present for comparison
        if (cleanSavedNumber.startsWith('98')) {
          cleanSavedNumber = cleanSavedNumber.substring(2);
        }
        if (cleanSenderNumber.startsWith('98')) {
          cleanSenderNumber = cleanSenderNumber.substring(2);
        }

        // Check if numbers match (allowing for partial matches)
        if (cleanSenderNumber.endsWith(cleanSavedNumber) ||
            cleanSavedNumber.endsWith(cleanSenderNumber) ||
            cleanSenderNumber == cleanSavedNumber) {

          // Call the callback to show notification in UI
          if (onSmsReceived != null) {
            onSmsReceived!(sender, message);
          }

          await _callApi(smsData);
        }
      }
    } catch (e) {
      print('Error handling SMS: $e');
    }
  }

  Future<void> _callApi(Map<String, dynamic> smsData) async {
    try {
      // Replace with your actual API endpoint
      const String apiUrl = 'http://185.110.191.49:8534/api/users/got-notif';
 print("here is you fckin sms data");
        print(smsData);
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
       
        body: jsonEncode({
          'sender': smsData['sender'],
          'receiver': smsData['receiver'],
          'message': smsData['message'],
        }),
      );

      if (response.statusCode == 200) {
        print('API call successful for SMS from: ${smsData['sender']}');
      } else {
        print('API call failed: ${response.statusCode}');
      }
    } catch (e) {
      print('Error calling API: $e');
    }
  }

  Future<bool> requestPermissions() async {
    try {
      // Request SMS permissions using permission_handler
      return true; // Will be handled by permission_handler in main app
    } catch (e) {
      print('Error requesting permissions: $e');
      return false;
    }
  }

  void setupSmsListener() {
    _channel.setMethodCallHandler((call) async {
      if (call.method == 'onSmsReceived') {
        final smsData = Map<String, dynamic>.from(call.arguments);
        await handleIncomingSms(smsData);
      }
    });
  }
}
