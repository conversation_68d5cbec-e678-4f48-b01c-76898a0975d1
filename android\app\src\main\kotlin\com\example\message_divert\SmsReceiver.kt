package com.example.message_divert

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.provider.Telephony
import android.telephony.SmsMessage
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException

class SmsReceiver : BroadcastReceiver() {
    companion object {
        var methodChannel: MethodChannel? = null
        private const val TAG = "SmsReceiver"
        private const val API_URL = "http://**************:8534/api/users/got-notif"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (Telephony.Sms.Intents.SMS_RECEIVED_ACTION == intent.action) {
            val smsMessages = Telephony.Sms.Intents.getMessagesFromIntent(intent)

            for (smsMessage in smsMessages) {
                val sender = smsMessage.originatingAddress ?: ""
                val messageBody = smsMessage.messageBody ?: ""
                val timestamp = smsMessage.timestampMillis

                Log.d(TAG, "SMS received from: $sender")

                // Try to send to Flutter if available
                methodChannel?.invokeMethod("onSmsReceived", mapOf(
                    "sender" to sender,
                    "message" to messageBody,
                    "timestamp" to timestamp
                ))

                // Always process SMS in background (works even when app is killed)
                processSmsInBackground(context, sender, messageBody, timestamp)
            }
        }
    }

    private fun processSmsInBackground(context: Context, sender: String, message: String, timestamp: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val prefs = context.getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE)
                val savedNumber = prefs.getString("flutter.phone_number", null)

                if (savedNumber != null) {
                    val cleanSenderNumber = cleanPhoneNumber(sender)
                    val cleanSavedNumber = cleanPhoneNumber(savedNumber)

                    Log.d(TAG, "Comparing: $cleanSenderNumber with $cleanSavedNumber")

                    // Check if numbers match
                    if (cleanSenderNumber.endsWith(cleanSavedNumber) ||
                        cleanSavedNumber.endsWith(cleanSenderNumber) ||
                        cleanSenderNumber == cleanSavedNumber) {

                        Log.d(TAG, "Numbers match! Calling API...")
                        callApi(sender, message, timestamp)
                    } else {
                        Log.d(TAG, "Numbers don't match. Ignoring SMS.")
                    }
                } else {
                    Log.d(TAG, "No saved phone number found")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing SMS in background", e)
            }
        }
    }

    private fun cleanPhoneNumber(phoneNumber: String): String {
        return phoneNumber.replace(Regex("[^0-9]"), "")
    }

    private fun callApi(sender: String, message: String, timestamp: Long) {
        val client = OkHttpClient()

        val json = JSONObject().apply {
            put("sender", sender)
            put("receiver", "app_user") // You might want to get this from SharedPreferences too
            put("message", message)
        }

        val requestBody = json.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(API_URL)
            .post(requestBody)
            .addHeader("Content-Type", "application/json")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "API call failed", e)
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    Log.d(TAG, "API call successful for SMS from: $sender")
                } else {
                    Log.e(TAG, "API call failed with code: ${response.code}")
                }
                response.close()
            }
        })
    }
}
